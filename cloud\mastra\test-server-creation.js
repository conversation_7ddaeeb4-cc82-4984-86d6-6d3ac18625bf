// Test Mastra server creation
import { <PERSON><PERSON> } from '@mastra/core';
import { createServer } from '@mastra/server';

console.log('Testing Mastra server creation...');

try {
  console.log('Creating Mastra instance...');
  const mastra = new Mastra({
    name: 'test-mastra',
    version: '1.0.0'
  });
  
  console.log('Mastra instance created');
  
  console.log('Creating server config...');
  const config = {
    server: {
      port: 3002
    }
  };
  
  console.log('Creating server...');
  const server = createServer(mastra, config);
  
  console.log('Server created successfully');
  console.log('Server type:', typeof server);
  
  console.log('Test completed successfully');
} catch (error) {
  console.error('Server creation test failed:', error);
  process.exit(1);
}
