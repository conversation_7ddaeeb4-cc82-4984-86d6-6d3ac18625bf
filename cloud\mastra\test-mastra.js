// Minimal <PERSON>stra test
import { <PERSON>stra } from '@mastra/core';

console.log('Testing Mastra instance creation...');

try {
  const mastra = new Mastra({
    name: 'test-mastra',
    version: '1.0.0'
  });
  
  console.log('Mastra instance created successfully');
  console.log('Mastra name:', mastra.name);
  
  // Test if we can get workflows (should be empty)
  const workflows = mastra.getWorkflows();
  console.log('Workflows:', Object.keys(workflows || {}));
  
  console.log('Mastra test completed successfully');
} catch (error) {
  console.error('<PERSON>stra test failed:', error);
  process.exit(1);
}
